from flask import Flask, render_template, jsonify
import json

app = Flask(__name__)

# Team data
TEAM_DATA = {
    "2024": {
        "year": "2024",
        "description": "Our inaugural season team that laid the foundation for our success.",
        "members": [
            {"name": "<PERSON>", "role": "Captain"},
            {"name": "<PERSON>", "role": "Captain"},
            {"name": "Sarve<PERSON>", "role": "<PERSON>er"},
            {"name": "<PERSON><PERSON>", "role": "Social Media"},
            {"name": "<PERSON><PERSON>", "role": "Social Media"}
        ]
    },
    "2025": {
        "year": "2025",
        "description": "Our current championship-caliber team pushing the boundaries of innovation.",
        "members": [
            {"name": "<PERSON><PERSON>", "role": "President"},
            {"name": "<PERSON>", "role": "Drive Team"},
            {"name": "<PERSON><PERSON><PERSON>", "role": "Drive Team"},
            {"name": "Sarvesh", "role": "Engineering Head"},
            {"name": "<PERSON><PERSON>", "role": "Project Manager"},
            {"name": "<PERSON>", "role": "Robot Technician & Building Head"},
            {"name": "<PERSON>", "role": "Safety Captain"},
            {"name": "<PERSON>", "role": "Human Player"},
            {"name": "Eli", "role": "Hardware Expert"}
        ]
    }
}

ACCOMPLISHMENTS = [
    {
        "year": "2024",
        "title": "Rookie All Star Award",
        "status": "Runner-up",
        "description": "In our inaugural season, we demonstrated exceptional rookie performance and team spirit, earning recognition as runners-up for the prestigious Rookie All Star Award.",
        "icon": "medal"
    },
    {
        "year": "2025",
        "title": "Overall Competition",
        "status": "Second Place",
        "description": "Building on our rookie success, we achieved an outstanding second overall placement, showcasing our team's growth and dedication to excellence.",
        "icon": "trophy"
    }
]

MISSION_VALUES = [
    {
        "icon": "lightbulb",
        "title": "Innovation",
        "description": "Pushing boundaries and thinking outside the box"
    },
    {
        "icon": "users",
        "title": "Teamwork",
        "description": "Collaborating to achieve extraordinary results"
    },
    {
        "icon": "trophy",
        "title": "Excellence",
        "description": "Striving for the highest standards in everything we do"
    }
]

@app.route('/')
def index():
    """Main page route"""
    return render_template('index.html', 
                         team_data=TEAM_DATA, 
                         accomplishments=ACCOMPLISHMENTS,
                         mission_values=MISSION_VALUES)

@app.route('/api/team/<year>')
def get_team_data(year):
    """API endpoint to get team data for a specific year"""
    if year in TEAM_DATA:
        return jsonify(TEAM_DATA[year])
    return jsonify({"error": "Team data not found"}), 404

@app.route('/api/accomplishments')
def get_accomplishments():
    """API endpoint to get accomplishments data"""
    return jsonify(ACCOMPLISHMENTS)

@app.route('/api/mission')
def get_mission():
    """API endpoint to get mission values"""
    return jsonify(MISSION_VALUES)

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "team": "The Gadgeteers #9670"})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
